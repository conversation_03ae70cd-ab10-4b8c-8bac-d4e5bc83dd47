package com.adins.esign.businesslogic.impl.interfacing;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmMsuser;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.MessageDeliveryReportLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrMessageDeliveryReport;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsResponse;
import com.adins.esign.util.MssTool;
import com.adins.exceptions.StatusCode;
import com.adins.framework.persistence.dao.model.AuditContext;

import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;

@Component
public class GenericJatisSmsLogic extends BaseLogic implements JatisSmsLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericJatisSmsLogic.class);

	@Autowired
	private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired
	private MessageDeliveryReportLogic messageDeliveryReportLogic;

	@Value("${jatis.sms.url}")
	private String sendSmsUrl;
	@Value("${jatis.sms.id}")
	private String jatisId;
	@Value("${jatis.sms.password}")
	private String jatisPassword;
	@Value("${jatis.sms.sender}")
	private String jatisSender;
	@Value("${jatis.sms.division}")
	private String jatisDivision;

	private static final String KEY_MSISDN = "msisdn";
	private static final String KEY_MESSAGE = "message";
	private static final String KEY_SENDER = "sender";
	private static final String KEY_DIVISION = "division";
	private static final String KEY_BATCHNAME = "batchname";
	private static final String KEY_UPLOADBY = "uploadby";
	private static final String KEY_CHANNEL = "channel";

	private static final String UPLOAD_BY_ESG_SYS = "ESIGNHUB SYSTEM";

	private void logSendSmsRequest(MsTenant tenant, RequestBody body) {
		Buffer buffer = new Buffer();
		try {
			body.writeTo(buffer);
			String requestBodyString = buffer.readUtf8();
			LOG.info("Send {} SMS Jatis request: {}", tenant.getTenantCode(), requestBodyString);
		} catch (Exception e) {
			LOG.error("Failed to log {} SMS Jatis request", tenant.getTenantCode());
		}
	}

	private JatisSmsResponse sendSms(JatisSmsRequestBean requestBean, SigningProcessAuditTrailBean auditTrailBean) {

		MsTenant tenant = requestBean.getTenant();
		String phoneNumber = requestBean.getPhoneNumber();
		String smsMessage = requestBean.getSmsMessage();
		boolean isOtpSms = requestBean.isOtpSms();
		String batchname = "SMS eSignHub " + tenant.getTenantCode();

		String formattedPhone = MssTool.changePrefixTo62(phoneNumber);

		String id = null;
		String password = null;
		String sender = null;
		String division = null;

		MsTenantSettings tsId = daoFactory.getTenantSettingsDao().getTenantSettings(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_SMS_JATIS_ID);
		MsTenantSettings tsPass = daoFactory.getTenantSettingsDao().getTenantSettings(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_SMS_JATIS_PASSWORD);
		MsTenantSettings tsSender = daoFactory.getTenantSettingsDao().getTenantSettings(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_SMS_JATIS_SENDER);
		MsTenantSettings tsDivision = daoFactory.getTenantSettingsDao().getTenantSettings(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_SMS_JATIS_DIVISION);

		if (null != tsId && StringUtils.isNotBlank(tsId.getSettingValue()) &&
				null != tsPass && StringUtils.isNotBlank(tsPass.getSettingValue()) &&
				null != tsSender && StringUtils.isNotBlank(tsSender.getSettingValue()) &&
				null != tsDivision && StringUtils.isNotBlank(tsDivision.getSettingValue())) {
			id = tsId.getSettingValue();
			password = tsPass.getSettingValue();
			sender = tsSender.getSettingValue();
			division = tsDivision.getSettingValue();
		} else {
			id = jatisId;
			password = jatisPassword;
			sender = jatisSender;
			division = jatisDivision;
		}

		RequestBody formBody = new FormBody.Builder()
				.add("userid", id)
				.add("password", password)
				.add(KEY_MSISDN, formattedPhone)
				.add(KEY_MESSAGE, smsMessage)
				.add(KEY_SENDER, sender)
				.add(KEY_DIVISION, division)
				.add(KEY_BATCHNAME, batchname)
				.add(KEY_UPLOADBY, UPLOAD_BY_ESG_SYS)
				.add(KEY_CHANNEL, isOtpSms ? "2" : "0")
				.build();

		RequestBody logFormBody = new FormBody.Builder()
				.add(KEY_MSISDN, formattedPhone)
				.add(KEY_MESSAGE, smsMessage)
				.add(KEY_SENDER, sender)
				.add(KEY_DIVISION, division)
				.add(KEY_BATCHNAME, batchname)
				.add(KEY_UPLOADBY, UPLOAD_BY_ESG_SYS)
				.add(KEY_CHANNEL, isOtpSms ? "2" : "0")
				.build();

		logSendSmsRequest(tenant, logFormBody);

		Request request = new Request.Builder()
				.url(sendSmsUrl)
				.post(formBody)
				.build();

		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(10, TimeUnit.SECONDS)
				.readTimeout(30, TimeUnit.SECONDS)
				.build();

		try {
			Date startTime = new Date();
			Response response = client.newCall(request).execute();
			Date finishTime = new Date();
			logProcessDuration("Send " + tenant.getTenantCode() + " Jatis SMS", startTime, finishTime);

			String rawResponse = response.body().string();
			LOG.info("Send {} SMS Jatis response: {}", tenant.getTenantCode(), rawResponse);

			return JatisSmsResponse.parseString(rawResponse);

		} catch (Exception e) {

			LOG.error("Send {} SMS Jatis exception: {}", tenant.getTenantCode(), e.getLocalizedMessage(), e);
			JatisSmsResponse jatisResponse = new JatisSmsResponse();
			jatisResponse.setStatus(String.valueOf(StatusCode.UNKNOWN));
			jatisResponse.setMessageId("Unknown system error");
			return jatisResponse;

		}

	}

	private JatisSmsResponse sendSms(JatisSmsRequestBean requestBean) {

		MsTenant tenant = requestBean.getTenant();
		String phoneNumber = requestBean.getPhoneNumber();
		String smsMessage = requestBean.getSmsMessage();
		boolean isOtpSms = requestBean.isOtpSms();
		String batchname = "SMS eSignHub " + tenant.getTenantCode();

		String formattedPhone = MssTool.changePrefixTo62(phoneNumber);

		String id = null;
		String password = null;
		String sender = null;
		String division = null;

		MsTenantSettings tsId = daoFactory.getTenantSettingsDao().getTenantSettings(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_SMS_JATIS_ID);
		MsTenantSettings tsPass = daoFactory.getTenantSettingsDao().getTenantSettings(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_SMS_JATIS_PASSWORD);
		MsTenantSettings tsSender = daoFactory.getTenantSettingsDao().getTenantSettings(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_SMS_JATIS_SENDER);
		MsTenantSettings tsDivision = daoFactory.getTenantSettingsDao().getTenantSettings(tenant,
				GlobalVal.CODE_LOV_TENANT_SETTING_SMS_JATIS_DIVISION);

		if (null != tsId && StringUtils.isNotBlank(tsId.getSettingValue()) &&
				null != tsPass && StringUtils.isNotBlank(tsPass.getSettingValue()) &&
				null != tsSender && StringUtils.isNotBlank(tsSender.getSettingValue()) &&
				null != tsDivision && StringUtils.isNotBlank(tsDivision.getSettingValue())) {
			id = tsId.getSettingValue();
			password = tsPass.getSettingValue();
			sender = tsSender.getSettingValue();
			division = tsDivision.getSettingValue();
		} else {
			id = jatisId;
			password = jatisPassword;
			sender = jatisSender;
			division = jatisDivision;
		}

		RequestBody formBody = new FormBody.Builder()
				.add("userid", id)
				.add("password", password)
				.add(KEY_MSISDN, formattedPhone)
				.add(KEY_MESSAGE, smsMessage)
				.add(KEY_SENDER, sender)
				.add(KEY_DIVISION, division)
				.add(KEY_BATCHNAME, batchname)
				.add(KEY_UPLOADBY, UPLOAD_BY_ESG_SYS)
				.add(KEY_CHANNEL, isOtpSms ? "2" : "0")
				.build();

		RequestBody logFormBody = new FormBody.Builder()
				.add(KEY_MSISDN, formattedPhone)
				.add(KEY_MESSAGE, smsMessage)
				.add(KEY_SENDER, sender)
				.add(KEY_DIVISION, division)
				.add(KEY_BATCHNAME, batchname)
				.add(KEY_UPLOADBY, UPLOAD_BY_ESG_SYS)
				.add(KEY_CHANNEL, isOtpSms ? "2" : "0")
				.build();

		logSendSmsRequest(tenant, logFormBody);

		Request request = new Request.Builder()
				.url(sendSmsUrl)
				.post(formBody)
				.build();

		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(10, TimeUnit.SECONDS)
				.readTimeout(30, TimeUnit.SECONDS)
				.build();

		try {

			Date startTime = new Date();
			Response response = client.newCall(request).execute();
			Date finishTime = new Date();
			logProcessDuration("Send " + tenant.getTenantCode() + " Jatis SMS", startTime, finishTime);

			String rawResponse = response.body().string();
			LOG.info("Send {} SMS Jatis response: {}", tenant.getTenantCode(), rawResponse);
			return JatisSmsResponse.parseString(rawResponse);

		} catch (Exception e) {

			LOG.error("Send {} SMS Jatis exception: {}", tenant.getTenantCode(), e.getLocalizedMessage(), e);
			JatisSmsResponse jatisResponse = new JatisSmsResponse();
			jatisResponse.setStatus(String.valueOf(StatusCode.UNKNOWN));
			jatisResponse.setMessageId("Unknown system error");
			return jatisResponse;

		}

	}

	@Override
	@Async
	public void sendSmsAndCutBalance(JatisSmsRequestBean request, TrDocumentH documentH, TrDocumentD document,
			AmMsuser user, String notes, AuditContext audit) {
		JatisSmsResponse response = sendSms(request);
		if (!"1".equals(response.getStatus())) {
			return;
		}

		String balanceTypeCode = request.isOtpSms() ? GlobalVal.CODE_LOV_BALANCE_TYPE_OTP
				: GlobalVal.CODE_LOV_BALANCE_TYPE_SMS;
		String trxTypeCode = request.isOtpSms() ? GlobalVal.CODE_LOV_TRX_TYPE_UOTP : GlobalVal.CODE_LOV_TRX_TYPE_USMS;

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
		MsLov lovBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				balanceTypeCode);
		MsLov lovTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE,
				trxTypeCode);

		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getTrxNo());
		mutation.setTrxDate(new Date());
		mutation.setQty(-1);
		mutation.setMsLovByLovTrxType(lovTrxType);
		mutation.setMsLovByLovBalanceType(lovBalanceType);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(request.getTenant());
		mutation.setMsVendor(vendor);
		mutation.setVendorTrxNo(response.getMessageId());
		mutation.setNotes(StringUtils.isBlank(notes) ? "Send SMS to " + request.getPhoneNumber() : notes);

		if (null != document) {
			mutation.setTrDocumentD(document);
		}

		if (null != documentH) {
			mutation.setRefNo(documentH.getRefNumber());
			mutation.setTrDocumentH(documentH);
		}

		if (null != user) {
			mutation.setAmMsuser(user);
		}

		if (null != request.getOffice()) {
			mutation.setMsOffice(request.getOffice());
		}

		if (null != request.getBusinessLine()) {
			mutation.setMsBusinessLine(request.getBusinessLine());
		}

		if (StringUtils.isNotBlank(request.getRefNo()) && null == documentH) {
			mutation.setRefNo(request.getRefNo());
		}

		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

	}

	@Override
	@Async
	public void sendSmsAndCutBalance(JatisSmsRequestBean request, TrDocumentH documentH, TrDocumentD document,
			AmMsuser user, String notes, AuditContext audit, SigningProcessAuditTrailBean auditTrailBean) {
		JatisSmsResponse response = sendSms(request, auditTrailBean);
		MsLov lovNotificationMedia = new MsLov();
		MsLov lovNotificationVendor = new MsLov();
		lovNotificationMedia = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA,
				GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
		lovNotificationVendor = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY,
				GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS);

		if (!"1".equals(response.getStatus())) {
			TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
			auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
			auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
			auditTrail.setEmail(auditTrailBean.getEmail());
			auditTrail.setAmMsUser(auditTrailBean.getUser());
			auditTrail.setMsTenant(auditTrailBean.getTenant());
			auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
			auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
			auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
			auditTrail.setOtpCode(auditTrailBean.getOtpCode());
			auditTrail.setResultStatus("0");
			auditTrail.setDtmCrt(new Date());
			auditTrail.setUsrCrt(auditTrailBean.getEmail());
			auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
			auditTrail.setNotes(auditTrailBean.getNotes());
			auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
			auditTrail.setNotificationVendor(lovNotificationVendor.getDescription());
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
			if (auditTrailBean.getDocumentDs() != null) {
				for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
					TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
					auditTrailDetail.setDtmCrt(new Date());
					auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
					auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
					auditTrailDetail.setTrDocumentD(docD);
					daoFactory.getSigningProcessAuditTrailDao()
							.insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);
					;
				}
			}
			return;
		}

		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
		auditTrail.setEmail(auditTrailBean.getEmail());
		auditTrail.setAmMsUser(auditTrailBean.getUser());
		auditTrail.setMsTenant(auditTrailBean.getTenant());
		auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
		auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
		auditTrail.setOtpCode(auditTrailBean.getOtpCode());
		auditTrail.setResultStatus("1");
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(auditTrailBean.getEmail());
		auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
		auditTrail.setNotes(auditTrailBean.getNotes());
		auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
		auditTrail.setNotificationVendor(lovNotificationVendor.getDescription());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
		if (auditTrailBean.getDocumentDs() != null) {
			for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
				TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
				auditTrailDetail.setDtmCrt(new Date());
				auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
				auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
				auditTrailDetail.setTrDocumentD(docD);
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);
				;
			}
		}

		String balanceTypeCode = request.isOtpSms() ? GlobalVal.CODE_LOV_BALANCE_TYPE_OTP
				: GlobalVal.CODE_LOV_BALANCE_TYPE_SMS;
		String trxTypeCode = request.isOtpSms() ? GlobalVal.CODE_LOV_TRX_TYPE_UOTP : GlobalVal.CODE_LOV_TRX_TYPE_USMS;

		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
		MsLov lovBalanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE,
				balanceTypeCode);
		MsLov lovTrxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE,
				trxTypeCode);

		TrBalanceMutation mutation = new TrBalanceMutation();
		mutation.setTrxNo(request.getTrxNo());
		mutation.setTrxDate(new Date());
		mutation.setQty(-1);
		mutation.setMsLovByLovTrxType(lovTrxType);
		mutation.setMsLovByLovBalanceType(lovBalanceType);
		mutation.setUsrCrt(audit.getCallerId());
		mutation.setDtmCrt(new Date());
		mutation.setMsTenant(request.getTenant());
		mutation.setMsVendor(vendor);
		mutation.setVendorTrxNo(response.getMessageId());
		mutation.setNotes(StringUtils.isBlank(notes) ? "Send SMS to " + request.getPhoneNumber() : notes);

		if (null != document) {
			mutation.setTrDocumentD(document);
		}

		if (null != documentH) {
			mutation.setRefNo(documentH.getRefNumber());
			mutation.setTrDocumentH(documentH);
		}

		if (null != user) {
			mutation.setAmMsuser(user);
		}

		if (null != request.getOffice()) {
			mutation.setMsOffice(request.getOffice());
		}

		if (null != request.getBusinessLine()) {
			mutation.setMsBusinessLine(request.getBusinessLine());
		}

		if (StringUtils.isNotBlank(request.getRefNo()) && null == documentH) {
			mutation.setRefNo(request.getRefNo());
		}

		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(mutation);

		messageDeliveryReportLogic.insertMessageDeliveryReport(request.getTenant(), vendor, request.getTrxNo(),
				response.getMessageId(), auditTrailBean.getPhone(), NotificationType.SMS_JATIS, lovNotificationVendor,
				auditTrailBean.getLovSendingPoint(), audit);

	}

	@Override
	public JatisSmsResponse sendSmsOnly(JatisSmsRequestBean request) {
		return sendSms(request);
	}

}
