package com.adins.esign.dataaccess.api;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.TrMessageDeliveryReport;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.webservices.model.GetListDeliveryReportForMessageCheckingResponse;
import com.adins.esign.webservices.model.GetListMessageDeliveryReportRequest;

public interface MessageDeliveryReportDao {

	List<Map<String, Object>> getListMessageDelivery(GetListMessageDeliveryReportRequest request, int min, int max,
			Date reportTimeStart, Date reportTimeEnd, Date requestTimeStart, Date requestTimeEnd, MsLov lov);

	Integer countListMessageDelivery(GetListMessageDeliveryReportRequest request, int min, int max,
			Date reportTimeStart, Date reportTimeEnd, Date requestTimeStart, Date requestTimeEnd, MsLov lov);

	TrMessageDeliveryReport getWhatsAppMessageDeliveryReport(MsTenant tenant, String phone, String deliveryStatus);

	GetListDeliveryReportForMessageCheckingResponse getListDeliveryReportForMessageChecking(String recipient,
			String messageMedia);

	void insertMessageDeliveryReport(TrMessageDeliveryReport messageDeliveryReport);

	Integer countNotificationByPeriod(TrDocumentH docH, MsLov sendingPoint, Date periodStartTime,
			String recipientDetail);

}
