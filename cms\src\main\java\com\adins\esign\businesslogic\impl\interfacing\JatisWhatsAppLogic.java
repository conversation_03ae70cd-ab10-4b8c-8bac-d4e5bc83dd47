package com.adins.esign.businesslogic.impl.interfacing;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.MessageDeliveryReportLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.constants.MediaType;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsTenantSettings;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrMessageDeliveryReport;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.jatis.JatisWhatsAppProperties;
import com.adins.esign.model.custom.jatis.JatisWhatsAppTemplateBean;
import com.adins.esign.model.custom.jatis.JatisWhatsAppTemplateComponentBean;
import com.adins.esign.model.custom.jatis.JatisWhatsAppTemplateComponentParameterBean;
import com.adins.esign.model.custom.jatis.JatisWhatsAppTemplateLanguageBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.webservices.model.SendWhatsAppRequest;
import com.adins.esign.webservices.model.jatis.SendWhatsAppMessageRequest;
import com.adins.esign.webservices.model.jatis.SendWhatsAppMessageResponse;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

@Component
public class JatisWhatsAppLogic extends BaseLogic implements WhatsAppLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(JatisWhatsAppLogic.class);
	
	@Value("${jatis.sendwa.baseurl}") private String sendMessageBaseUrl;
	@Value("${jatis.sendwa.accountid}") private String accountId;
	@Value("${jatis.sendwa.token}") private String jatisToken;
	@Value("${jatis.sendwa.template.suffix}") private String templateSuffix;
	
	@Autowired private Gson gson;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private MessageDeliveryReportLogic messageDeliveryReportLogic;
	
	private static final String SEND_WA_TO_NOTE = "Sending WhatsApp to ";
	private static final String SEND_WA_EXCEPTION_LOG = "Send WhatsApp exception: {}";
	
	private JatisWhatsAppTemplateComponentBean getHeaderComponent(MsMsgTemplate messageTemplate) {
		List<JatisWhatsAppTemplateComponentParameterBean> headerParameters = new ArrayList<>();
		
		JatisWhatsAppTemplateComponentParameterBean headerParameter = new JatisWhatsAppTemplateComponentParameterBean();
		headerParameter.setType("text");
		headerParameter.setText(messageTemplate.getSubject());
		
		headerParameters.add(headerParameter);
		
		JatisWhatsAppTemplateComponentBean headerComponent = new JatisWhatsAppTemplateComponentBean();
		headerComponent.setType("header");
		headerComponent.setParameters(headerParameters);
		return headerComponent;
	}
	
	private JatisWhatsAppTemplateComponentBean getBodyComponent(List<String> bodyTexts) {
		List<JatisWhatsAppTemplateComponentParameterBean> bodyParameters = new ArrayList<>();
		for (String body : bodyTexts) {
			JatisWhatsAppTemplateComponentParameterBean bodyParameter = new JatisWhatsAppTemplateComponentParameterBean();
			bodyParameter.setType("text");
			bodyParameter.setText(body);
			bodyParameters.add(bodyParameter);
		}
		
		JatisWhatsAppTemplateComponentBean bodyComponent = new JatisWhatsAppTemplateComponentBean();
		bodyComponent.setType("body");
		bodyComponent.setParameters(bodyParameters);
		return bodyComponent;
	}
	
	private JatisWhatsAppTemplateComponentBean getButtonComponent(String buttonText) {
		List<JatisWhatsAppTemplateComponentParameterBean> buttonParameters = new ArrayList<>();
		
		JatisWhatsAppTemplateComponentParameterBean buttonParameter = new JatisWhatsAppTemplateComponentParameterBean();
		buttonParameter.setType("text");
		buttonParameter.setText(buttonText);
		buttonParameters.add(buttonParameter);
		
		JatisWhatsAppTemplateComponentBean buttonComponent = new JatisWhatsAppTemplateComponentBean();
		buttonComponent.setType("button");
		buttonComponent.setIndex(0);
		buttonComponent.setSubType("url");
		buttonComponent.setParameters(buttonParameters);
		return buttonComponent;
	}
	
	private SendWhatsAppMessageRequest prepareSendWhatsAppRequest(MsMsgTemplate messageTemplate, List<String> bodyTexts, String buttonText, String reservedTrxNo, String recipient, boolean removeHeader) {
		
		String phoneNumber = Character.compare(recipient.charAt(0), '0') == 0 ? "+62" + recipient.substring(1) : recipient;
		
		JatisWhatsAppTemplateLanguageBean language = new JatisWhatsAppTemplateLanguageBean();
		language.setPolicy("deterministic");
		language.setCode("id");
		
		List<JatisWhatsAppTemplateComponentBean> components = new ArrayList<>();
		if (!removeHeader) {
			components.add(getHeaderComponent(messageTemplate));
		}
		
		components.add(getBodyComponent(bodyTexts));
		
		if (StringUtils.isNotEmpty(buttonText)) {
			components.add(getButtonComponent(buttonText));
		}
		
		JatisWhatsAppTemplateBean template = new JatisWhatsAppTemplateBean();
		template.setName(messageTemplate.getWaTemplateCode() + templateSuffix);
		template.setLanguage(language);
		template.setComponents(components);
		
		SendWhatsAppMessageRequest request = new SendWhatsAppMessageRequest();
		request.setXid(reservedTrxNo);
		request.setTo(phoneNumber);
		request.setType("template");
		request.setPreviewUrl(true);
		request.setTemplate(template);
		return request;
	}
	
	private SendWhatsAppMessageResponse callSendWhatsAppApi(JatisWhatsAppProperties properties, MsMsgTemplate messageTemplate, List<String> bodyTexts, String buttonText, String reservedTrxNo, String recipient, boolean removeHeader) throws IOException {
		MultivaluedMap<String, String> mapHeader = new MultivaluedHashMap<>();
		mapHeader.add(MediaType.CONTENT_TYPE, MediaType.APPLICATION_JSON);
		mapHeader.add(MediaType.AUTHORIZATION, HttpHeaders.buildBearerToken(properties.getToken()));
		mapHeader.add("x-access-token", properties.getToken());
		
		// Request Body
		SendWhatsAppMessageRequest request = prepareSendWhatsAppRequest(messageTemplate, bodyTexts, buttonText, reservedTrxNo, recipient, removeHeader);
		String jsonRequest = gson.toJson(request);
		
		String completeUrl = sendMessageBaseUrl.replace("{x}", properties.getAccountId());
		LOG.info("Send WhatsApp message URL: {}", completeUrl);
		WebClient client = WebClient.create(completeUrl).headers(mapHeader);
		MssTool.setWebClientConnReadTimeout(client, 10_000L, 60_000L);
		
		LOG.info("Send WhatsApp message request: {}", jsonRequest);
		Response response = client.post(jsonRequest);
		InputStreamReader reader = new InputStreamReader((InputStream) response.getEntity());
		String jsonResponse = IOUtils.toString(reader);
		LOG.info("Send WhatsApp message response: {}", jsonResponse);
		return gson.fromJson(jsonResponse, SendWhatsAppMessageResponse.class);
	}
	
	@Override
	public boolean needtoCutBalance(MsTenant tenant, String phoneNumber, boolean isOtp) {
		if (isOtp) {
			return true;
		}
		
		TrMessageDeliveryReport report = daoFactory.getMessageDeliveryReportDao().getWhatsAppMessageDeliveryReport(tenant, phoneNumber, "1");
		if (null == report) {
			return true;
		}
		
		Date lastSessionTime = report.getReportTime();
		Date currentTime = new Date();
		long oneDayMillis = 86_400_000L; // 24 * 60 * 60 * 1000
		
		// Cut balance only last session has passed 24 hours
		return (currentTime.getTime() - lastSessionTime.getTime()) >=  oneDayMillis;
	}

	@Async
	@Override
	public void sendMessage(SendWhatsAppRequest request, AuditContext audit) {
		
		JatisWhatsAppProperties tenantProperties = getTenantWhatsAppProperties(request.getMsTenant());
		MsMsgTemplate messageTemplate = request.getTemplate();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();
		String reservedTrxNo = request.getReservedTrxNo();
		String recipient = request.getPhoneNumber();
				
		String balanceTypeCode = request.isOtp() ? GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP : GlobalVal.CODE_LOV_BALANCE_TYPE_WA;
		String trxTypeCode = request.isOtp() ? GlobalVal.CODE_LOV_TRX_TYPE_UWA_OTP : GlobalVal.CODE_LOV_TRX_TYPE_UWA;
		
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, trxTypeCode);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
		
		TrBalanceMutation balanceMutation = new TrBalanceMutation();
		balanceMutation.setTrxNo(reservedTrxNo);
		balanceMutation.setTrxDate(new Date());	
		balanceMutation.setQty(0);
		balanceMutation.setMsLovByLovBalanceType(balanceType);
		balanceMutation.setMsLovByLovTrxType(trxType);
		balanceMutation.setUsrCrt(StringUtils.left(audit.getCallerId(), 36));
		balanceMutation.setDtmCrt(new Date());
		balanceMutation.setMsTenant(request.getMsTenant());
		balanceMutation.setMsVendor(vendor);
		if (null != request.getAmMsuser()) {
			balanceMutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			balanceMutation.setTrDocumentH(request.getTrDocumentH());
			balanceMutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			balanceMutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			balanceMutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(balanceMutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			balanceMutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = SEND_WA_TO_NOTE + recipient;
		} else {
			notes = request.getNotes();
		}
		balanceMutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(balanceMutation);
		
		boolean isSuccess = true;
		try {
			SendWhatsAppMessageResponse response = callSendWhatsAppApi(tenantProperties, messageTemplate, bodyTexts, buttonText, reservedTrxNo, recipient, request.isRemoveHeader());
			isSuccess = response.getError() == null;
		} catch (Exception e) {
			isSuccess = false;
			LOG.error(SEND_WA_EXCEPTION_LOG, e.getLocalizedMessage(), e);
		}
			
		balanceMutation.setQty(isSuccess ? -1 : 0);
		balanceMutation.setUsrUpd(audit.getCallerId());
		balanceMutation.setDtmUpd(new Date());
		daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
		
	}
	
	@Override
	public boolean sendMessageNotAsync(SendWhatsAppRequest request, AuditContext audit) {
		
		JatisWhatsAppProperties tenantProperties = getTenantWhatsAppProperties(request.getMsTenant());
		MsMsgTemplate messageTemplate = request.getTemplate();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();
		String reservedTrxNo = request.getReservedTrxNo();
		String recipient = request.getPhoneNumber();
				
		String balanceCode = request.isOtp() ? GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP : GlobalVal.CODE_LOV_BALANCE_TYPE_WA;
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceCode);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
		MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);
		MsTenant tenant = request.getMsTenant();

		TrBalanceMutation balanceMutation = new TrBalanceMutation();
		balanceMutation.setTrxNo(reservedTrxNo);
		balanceMutation.setTrxDate(new Date());	
		balanceMutation.setQty(0);
		balanceMutation.setMsLovByLovBalanceType(balanceType);
		balanceMutation.setMsLovByLovTrxType(trxType);
		balanceMutation.setUsrCrt(StringUtils.left(audit.getCallerId(), 36));
		balanceMutation.setDtmCrt(new Date());
		balanceMutation.setMsTenant(request.getMsTenant());
		balanceMutation.setMsVendor(vendor);
		if (null != request.getAmMsuser()) {
			balanceMutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			balanceMutation.setTrDocumentH(request.getTrDocumentH());
			balanceMutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			balanceMutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			balanceMutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isNotBlank(request.getRefNo()) && StringUtils.isBlank(balanceMutation.getRefNo())) {
			balanceMutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = SEND_WA_TO_NOTE + recipient;
		} else {
			notes = request.getNotes();
		}
		balanceMutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(balanceMutation);
		
		boolean isSuccess = true;
		try {
			SendWhatsAppMessageResponse response = callSendWhatsAppApi(tenantProperties, messageTemplate, bodyTexts, buttonText, reservedTrxNo, recipient, request.isRemoveHeader());
			isSuccess = response.getError() == null;
		} catch (Exception e) {
			isSuccess = false;
			LOG.error(SEND_WA_EXCEPTION_LOG, e.getLocalizedMessage(), e);
		}
		
		balanceMutation.setQty(isSuccess ? -1 : 0);
		balanceMutation.setUsrUpd(audit.getCallerId());
		balanceMutation.setDtmUpd(new Date());
		daoFactory.getBalanceMutationDao().updateTrBalanceMutation(balanceMutation);

		if(isSuccess){
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, reservedTrxNo, null, recipient, NotificationType.WHATSAPP, messageGateway, sendingPoint, audit);
		}

		return isSuccess;
	}
	
	private JatisWhatsAppProperties getTenantWhatsAppProperties(MsTenant tenant) {
		MsTenantSettings accountIdSetting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "WHATSAPP_JATIS_ACCOUNT_ID");
		if (null == accountIdSetting) {
			return getApplicationWhatsAppProperties(tenant);
		}
		
		MsTenantSettings tokenSetting = daoFactory.getTenantSettingsDao().getTenantSettings(tenant, "WHATSAPP_JATIS_TOKEN");
		if (null == tokenSetting) {
			return getApplicationWhatsAppProperties(tenant);
		}
		
		LOG.debug("Tenant {} uses Jatis WhatsApp properties from tenant settings", tenant.getTenantCode());
		return new JatisWhatsAppProperties(accountIdSetting.getSettingValue(), tokenSetting.getSettingValue());
	}
	
	private JatisWhatsAppProperties getApplicationWhatsAppProperties(MsTenant tenant) {
		LOG.debug("Tenant {} uses Jatis WhatsApp properties from application", tenant.getTenantCode());
		return new JatisWhatsAppProperties(accountId, jatisToken);
	}

	@Async
	@Override
	public void sendMessage(SendWhatsAppRequest request, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {

		JatisWhatsAppProperties tenantProperties = getTenantWhatsAppProperties(request.getMsTenant());
		MsMsgTemplate messageTemplate = request.getTemplate();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();
		String reservedTrxNo = request.getReservedTrxNo();
		String recipient = request.getPhoneNumber();
				
		String balanceTypeCode = request.isOtp() ? GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP : GlobalVal.CODE_LOV_BALANCE_TYPE_WA;
		String trxTypeCode = request.isOtp() ? GlobalVal.CODE_LOV_TRX_TYPE_UWA_OTP : GlobalVal.CODE_LOV_TRX_TYPE_UWA;
		
		MsTenant tenant = request.getMsTenant();
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceTypeCode);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, trxTypeCode);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
		MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);

		TrBalanceMutation balanceMutation = new TrBalanceMutation();
		balanceMutation.setTrxNo(reservedTrxNo);
		balanceMutation.setTrxDate(new Date());	
		balanceMutation.setQty(0);
		balanceMutation.setMsLovByLovBalanceType(balanceType);
		balanceMutation.setMsLovByLovTrxType(trxType);
		balanceMutation.setUsrCrt(StringUtils.left(audit.getCallerId(), 36));
		balanceMutation.setDtmCrt(new Date());
		balanceMutation.setMsTenant(request.getMsTenant());
		balanceMutation.setMsVendor(vendor);
		if (null != request.getAmMsuser()) {
			balanceMutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			balanceMutation.setTrDocumentH(request.getTrDocumentH());
			balanceMutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			balanceMutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			balanceMutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isBlank(balanceMutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
			balanceMutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = SEND_WA_TO_NOTE + recipient;
		} else {
			notes = request.getNotes();
		}
		balanceMutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutationNewTran(balanceMutation);
		
		String resultStatus = "1";
		
		boolean isSuccess = true;
		try {
			SendWhatsAppMessageResponse response = callSendWhatsAppApi(tenantProperties, messageTemplate, bodyTexts, buttonText, reservedTrxNo, recipient, request.isRemoveHeader());
			isSuccess = response.getError() == null;
			resultStatus = response.getError() == null ? "1" : "0";
		} catch (Exception e) {
			isSuccess = false;
			resultStatus = "0";
			LOG.error(SEND_WA_EXCEPTION_LOG, e.getLocalizedMessage(), e);
		}
			
		balanceMutation.setQty(isSuccess ? -1 : 0);
		balanceMutation.setUsrUpd(audit.getCallerId());
		balanceMutation.setDtmUpd(new Date());
		daoFactory.getBalanceMutationDao().updateTrBalanceMutationNewTran(balanceMutation);
		
		if(isSuccess){
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, reservedTrxNo, null, recipient, NotificationType.WHATSAPP, messageGateway, auditTrailBean.getLovSendingPoint(), audit);
		}

		MsLov lovNotificationMedia = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_MESSAGE_MEDIA, GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
		MsLov lovNotificationVendor = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
		auditTrail.setEmail(StringUtils.upperCase(auditTrailBean.getEmail()));
		auditTrail.setAmMsUser(auditTrailBean.getUser());
		auditTrail.setMsTenant(auditTrailBean.getTenant());
		auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
		auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
		auditTrail.setOtpCode(auditTrailBean.getOtpCode());
		auditTrail.setNotificationMedia(lovNotificationMedia.getDescription());
		auditTrail.setNotificationVendor(lovNotificationVendor.getDescription());
		auditTrail.setNotes(auditTrailBean.getNotes());
		auditTrail.setResultStatus(resultStatus);
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(auditTrailBean.getEmail());
		auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
		
		if (auditTrailBean.getDocumentDs() == null) {
			return;
		}
		
		for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
			auditTrailDetail.setDtmCrt(new Date());
			auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
			auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
			auditTrailDetail.setTrDocumentD(docD);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);
		}
	}
	
	@Override
	public boolean sendMessageNotAsync(SendWhatsAppRequest request, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {
		
		JatisWhatsAppProperties tenantProperties = getTenantWhatsAppProperties(request.getMsTenant());
		MsMsgTemplate messageTemplate = request.getTemplate();
		List<String> bodyTexts = request.getBodyTexts();
		String buttonText = request.getButtonText();
		String reservedTrxNo = request.getReservedTrxNo();
		String recipient = request.getPhoneNumber();
			
		String balanceCode = request.isOtp() ? GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP : GlobalVal.CODE_LOV_BALANCE_TYPE_WA;
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceCode);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
		MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);

		TrBalanceMutation balanceMutation = new TrBalanceMutation();
		balanceMutation.setTrxNo(reservedTrxNo);
		balanceMutation.setTrxDate(new Date());	
		balanceMutation.setQty(0);
		balanceMutation.setMsLovByLovBalanceType(balanceType);
		balanceMutation.setMsLovByLovTrxType(trxType);
		balanceMutation.setUsrCrt(StringUtils.left(audit.getCallerId(), 36));
		balanceMutation.setDtmCrt(new Date());
		balanceMutation.setMsTenant(request.getMsTenant());
		balanceMutation.setMsVendor(vendor);
		if (null != request.getAmMsuser()) {
			balanceMutation.setAmMsuser(request.getAmMsuser());
		}
		if (null != request.getTrDocumentH()) {
			balanceMutation.setTrDocumentH(request.getTrDocumentH());
			balanceMutation.setRefNo(request.getTrDocumentH().getRefNumber());
		}
		if (null != request.getMsBusinessLine()) {
			balanceMutation.setMsBusinessLine(request.getMsBusinessLine());
		}
		if (null != request.getMsOffice()) {
			balanceMutation.setMsOffice(request.getMsOffice());
		}
		if (StringUtils.isNotBlank(request.getRefNo()) && StringUtils.isBlank(balanceMutation.getRefNo())) {
			balanceMutation.setRefNo(request.getRefNo());
		}
		String notes = null;
		if (StringUtils.isBlank(request.getNotes())) {
			notes = SEND_WA_TO_NOTE + recipient;
		} else {
			notes = request.getNotes();
		}
		balanceMutation.setNotes(notes);
		daoFactory.getBalanceMutationDao().insertTrBalanceMutation(balanceMutation);

		String resultStatus = "1";
		
		boolean isSuccess = true;
		try {
			SendWhatsAppMessageResponse response = callSendWhatsAppApi(tenantProperties, messageTemplate, bodyTexts, buttonText, reservedTrxNo, recipient, request.isRemoveHeader());
			isSuccess = response.getError() == null;
			resultStatus = response.getError() == null ? "1" : "0";
		} catch (Exception e) {
			isSuccess = false;
			resultStatus = "0";
			LOG.error(SEND_WA_EXCEPTION_LOG, e.getLocalizedMessage(), e);
		}
		
		balanceMutation.setQty(isSuccess ? -1 : 0);
		balanceMutation.setUsrUpd(audit.getCallerId());
		balanceMutation.setDtmUpd(new Date());
		daoFactory.getBalanceMutationDao().updateTrBalanceMutation(balanceMutation);
		if(isSuccess){
			messageDeliveryReportLogic.insertMessageDeliveryReport(request.getMsTenant(), vendor, reservedTrxNo, null, recipient, NotificationType.WHATSAPP, messageGateway, auditTrailBean.getLovSendingPoint(), audit);
		}

		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
		auditTrail.setEmail(StringUtils.upperCase(auditTrailBean.getEmail()));
		auditTrail.setAmMsUser(auditTrailBean.getUser());
		auditTrail.setMsTenant(auditTrailBean.getTenant());
		auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
		auditTrail.setLovSendingPoint(auditTrailBean.getLovSendingPoint());
		auditTrail.setLovProcessType(auditTrailBean.getLovProcessType());
		auditTrail.setOtpCode(auditTrailBean.getOtpCode());
		auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
		auditTrail.setNotificationVendor(GlobalVal.NOTIFICATION_VENDOR_NAME_WHATSAPP_JATIS);
		auditTrail.setNotes(auditTrailBean.getNotes());
		auditTrail.setResultStatus(resultStatus);
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(auditTrailBean.getEmail());
		auditTrail.setTrInvitationLink(auditTrailBean.getInvLink());
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
		
		if (auditTrailBean.getDocumentDs() == null) {
			return isSuccess;
		}
		
		for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
			auditTrailDetail.setDtmCrt(new Date());
			auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
			auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
			auditTrailDetail.setTrDocumentD(docD);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetail(auditTrailDetail);
		}

		return isSuccess;
	}

}
